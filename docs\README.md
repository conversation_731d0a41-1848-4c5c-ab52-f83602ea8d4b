# RefundGo Documentation

Welcome to the comprehensive documentation for RefundGo, a multilingual task publishing and completion platform built with Next.js 14+ and modern React patterns.

## 📚 Documentation Structure

### 🚀 Getting Started
- **[Project Overview](project-context.md)** - Application purpose, features, and tech stack
- **[Development Guide](development-guide.md)** - Setup, patterns, and best practices
- **[Environment Setup](getting-started/vscode-setup.md)** - VSCode configuration and development tools
- **[Code Quality](getting-started/eslint-configuration.md)** - ESLint configuration and standards

### 🏗️ Architecture & Design

- **[System Architecture](architecture/system-architecture.md)** - Technical architecture and design patterns
- **[Database Schema](project-context.md#database-schema-prisma)** - Data models and relationships
- **[API Reference](project-context.md#api-endpoints)** - API endpoints and patterns
- **[User Roles](architecture/user-roles.md)** - User permissions and access control
- **[Feature Modules](architecture/feature-modules.md)** - Modular development approach

### 🔧 Development

- **[Testing Guide](testing-guide.md)** - Testing strategies and examples
- **[User Interaction Guide](user-interaction-guide.md)** - User experience patterns
- **[AI Assistant Guide](ai-assistant-guide.md)** - AI integration and usage

### 🌍 Features & Integrations

- **[Internationalization](features/internationalization.md)** - Complete i18n implementation guide
- **[Email System](features/email-system.md)** - Email templates, notifications, and language detection
- **[Payment System](features/currency-conversion.md)** - Payment processing and currency handling
- **[Authentication](project-context.md#authentication)** - NextAuth v5 implementation
- **[Cron Jobs](features/cron-job-setup.md)** - Scheduled tasks and automation

### 🎨 UI Components & Design

- **[Component Library](ui-components/component-library.md)** - Complete component documentation and patterns
- **[Responsive Design](ui-components/responsive-design.md)** - Mobile-first responsive implementation
- **[Theme System](ui-components/DARK_MODE_TEXT_VISIBILITY_FIX.md)** - Dark mode and theming
- **[Navigation Components](ui-components/NAVBAR_OVERFLOW_FIX.md)** - Navigation fixes and patterns

### 🚀 Deployment & Operations

- **[Production Deployment](project-context.md#deployment)** - Build and deployment process
- **[Environment Configuration](project-context.md#environment-variables)** - Environment variables
- **[Monitoring](project-context.md#monitoring-and-operations)** - Error tracking and performance

### 📋 Implementation History

- **[Recent Fixes](changelog/recent-fixes.md)** - Consolidated bug fixes and improvements
- **[Logo Implementation](changelog/LOGO_FIXES_SUMMARY.md)** - Logo system fixes and enhancements
- **[Footer Redesign](changelog/footer-final-summary.md)** - Footer simplification and improvements
- **[CSS Refactoring](changelog/css-refactoring-summary.md)** - Performance improvements and optimization
- **[Email Enhancements](changelog/email-system-implementation-summary.md)** - Email system improvements
- **[Responsive Implementation](implementation/)** - Mobile responsiveness improvements

## 🔍 Quick Navigation

### For New Developers

1. Start with [Project Overview](project-context.md)
2. Follow [Development Guide](development-guide.md) for setup
3. Review [Testing Guide](testing-guide.md) for quality standards

### For Feature Development

1. Check [Feature Modules](architecture/feature-modules.md) for architecture
2. Review [API Reference](project-context.md#api-endpoints) for endpoints
3. Follow [Code Quality](getting-started/eslint-configuration.md) standards

### For UI/UX Work

1. Review [Component Library](ui-components/component-library.md) for branding
2. Check [Responsive Design](ui-components/responsive-design.md) for mobile patterns
3. Follow [Theme System](ui-components/DARK_MODE_TEXT_VISIBILITY_FIX.md) for consistency

### For Deployment

1. Review [Environment Configuration](project-context.md#environment-variables)
2. Follow [Production Deployment](project-context.md#deployment) guide
3. Set up [Monitoring](project-context.md#monitoring-and-operations)

## 🛠️ Key Technologies

- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript 5.8+ (strict mode)
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth v5 (beta)
- **UI**: Tailwind CSS, shadcn/ui, Radix UI
- **State Management**: TanStack Query, Zustand
- **Internationalization**: next-intl
- **Testing**: Jest, React Testing Library, Playwright

## 📖 Documentation Standards

### File Organization

- Core documentation in root `/docs` folder
- Implementation details in `/docs/implementation/`
- Testing documentation in `/docs/testing/`
- Archive old content in `/docs/archive-old/`

### Naming Conventions

- Use kebab-case for file names
- Include descriptive prefixes (e.g., `api-`, `ui-`, `fix-`)
- Use `.md` extension for all documentation

### Content Guidelines

- Start with clear overview and objectives
- Include code examples where relevant
- Maintain consistent formatting
- Update modification dates
- Cross-reference related documents

## 🔗 External Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [NextAuth.js](https://authjs.dev/)
- [next-intl](https://next-intl-docs.vercel.app/)

## 📝 Contributing to Documentation

1. Follow the established structure and naming conventions
2. Update this README when adding new documentation
3. Ensure all internal links are functional
4. Include relevant code examples and implementation details
5. Maintain consistent formatting and style

---

**Last Updated**: 2025-01-29  
**Documentation Version**: 2.0  
**Project Version**: RefundGo Web 2.0
